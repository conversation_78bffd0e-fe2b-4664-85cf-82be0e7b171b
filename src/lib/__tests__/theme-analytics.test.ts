import * as Sentry from '@sentry/nextjs';

import { ThemeAnalytics } from '../theme-analytics';

// Mock Sentry
jest.mock('@sentry/nextjs', () => ({
  setTag: jest.fn(),
  setUser: jest.fn(),
  addBreadcrumb: jest.fn(),
  setMeasurement: jest.fn(),
  setContext: jest.fn(),
  captureMessage: jest.fn(),
}));

// Mock crypto for secure random
const mockGetRandomValues = jest.fn();
Object.defineProperty(global, 'crypto', {
  value: {
    getRandomValues: mockGetRandomValues,
  },
  writable: true,
});

describe('ThemeAnalytics', () => {
  let analytics: ThemeAnalytics;

  beforeEach(() => {
    jest.clearAllMocks();

    // Reset mockGetRandomValues to default behavior
    mockGetRandomValues.mockReset();
    mockGetRandomValues.mockImplementation((array) => {
      array[0] = 0x40000000; // 25% value for consistent testing
      return array;
    });

    analytics = new ThemeAnalytics({
      enabled: true,
      sampleRate: 1.0, // 100% sampling for tests
      performanceThreshold: 100,
    });

    // Mock Date.now for consistent timestamps
    jest.spyOn(Date, 'now').mockReturnValue(1000);

    // Mock navigator.userAgent
    Object.defineProperty(navigator, 'userAgent', {
      value: 'Test Browser',
      writable: true,
    });

    // Mock window dimensions (only if window exists)
    if (typeof window !== 'undefined') {
      Object.defineProperty(window, 'innerWidth', {
        value: 1024,
        writable: true,
      });
      Object.defineProperty(window, 'innerHeight', {
        value: 768,
        writable: true,
      });
    }
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('初始化和配置', () => {
    it('should initialize with default config', () => {
      new ThemeAnalytics();

      expect(Sentry.setTag).toHaveBeenCalledWith('feature', 'theme-analytics');
    });

    it('should respect disabled config', () => {
      const disabledAnalytics = new ThemeAnalytics({ enabled: false });

      disabledAnalytics.recordThemeSwitch('light', 'dark', 1000, 1100);

      expect(Sentry.addBreadcrumb).not.toHaveBeenCalled();
    });

    it('should use custom config values', () => {
      const customAnalytics = new ThemeAnalytics({
        performanceThreshold: 200,
        sampleRate: 0.5,
        enableDetailedTracking: false,
      });

      expect(customAnalytics).toBeDefined();
    });
  });

  describe('采样率控制', () => {
    it('should respect 0% sampling rate', () => {
      const zeroSampleAnalytics = new ThemeAnalytics({
        enabled: true,
        sampleRate: 0,
      });

      zeroSampleAnalytics.recordThemeSwitch('light', 'dark', 1000, 1100);

      expect(Sentry.addBreadcrumb).not.toHaveBeenCalled();
    });

    it('should use crypto.getRandomValues when available', () => {
      const mockArray = new Uint32Array([0x80000000]); // 50% value
      mockGetRandomValues.mockImplementation((array) => {
        array[0] = mockArray[0];
        return array;
      });

      const analytics50 = new ThemeAnalytics({
        enabled: true,
        sampleRate: 0.6, // Should pass with 50% random value
      });

      analytics50.recordThemeSwitch('light', 'dark', 1000, 1100);

      expect(mockGetRandomValues).toHaveBeenCalled();
      expect(Sentry.addBreadcrumb).toHaveBeenCalled();
    });

    it('should fallback to Math.random when crypto unavailable', () => {
      const originalCrypto = global.crypto;
      // @ts-expect-error - Testing edge case
      delete global.crypto;

      jest.spyOn(Math, 'random').mockReturnValue(0.3); // 30% value

      const analytics50 = new ThemeAnalytics({
        enabled: true,
        sampleRate: 0.5,
      });

      analytics50.recordThemeSwitch('light', 'dark', 1000, 1100);

      expect(Math.random).toHaveBeenCalled();
      expect(Sentry.addBreadcrumb).toHaveBeenCalled();

      // Restore crypto
      global.crypto = originalCrypto;
    });

    it('should handle crypto.getRandomValues throwing an error', () => {
      // Create analytics instance first
      const analytics50 = new ThemeAnalytics({
        enabled: true,
        sampleRate: 0.5,
      });

      // Then set up the mock to throw an error
      const originalImplementation =
        mockGetRandomValues.getMockImplementation();
      mockGetRandomValues.mockImplementation(() => {
        throw new Error('getRandomValues failed');
      });

      // When crypto.getRandomValues throws, the error should propagate
      expect(() => {
        analytics50.recordThemeSwitch('light', 'dark', 1000, 1100);
      }).toThrow('getRandomValues failed');

      // Restore original implementation
      if (originalImplementation) {
        mockGetRandomValues.mockImplementation(originalImplementation);
      } else {
        mockGetRandomValues.mockReset();
        mockGetRandomValues.mockImplementation((array) => {
          array[0] = 0x40000000; // 25% value for consistent testing
          return array;
        });
      }
    });

    it('should handle crypto.getRandomValues as null', () => {
      const originalCrypto = global.crypto;
      global.crypto = { getRandomValues: null } as any;

      jest.spyOn(Math, 'random').mockReturnValue(0.3); // 30% value

      const analytics50 = new ThemeAnalytics({
        enabled: true,
        sampleRate: 0.5,
      });

      analytics50.recordThemeSwitch('light', 'dark', 1000, 1100);

      expect(Math.random).toHaveBeenCalled();
      expect(Sentry.addBreadcrumb).toHaveBeenCalled();

      // Restore crypto
      global.crypto = originalCrypto;
    });

    it('should handle edge case sampling rates', () => {
      // Test with exactly 0
      const zeroAnalytics = new ThemeAnalytics({
        enabled: true,
        sampleRate: 0,
      });
      zeroAnalytics.recordThemeSwitch('light', 'dark', 1000, 1100);
      expect(Sentry.addBreadcrumb).not.toHaveBeenCalled();

      jest.clearAllMocks();

      // Test with exactly 1
      const fullAnalytics = new ThemeAnalytics({
        enabled: true,
        sampleRate: 1,
      });
      fullAnalytics.recordThemeSwitch('light', 'dark', 1000, 1100);
      expect(Sentry.addBreadcrumb).toHaveBeenCalled();

      jest.clearAllMocks();

      // Test with negative value (should be treated as 0)
      const negativeAnalytics = new ThemeAnalytics({
        enabled: true,
        sampleRate: -0.1,
      });
      negativeAnalytics.recordThemeSwitch('light', 'dark', 1000, 1100);
      expect(Sentry.addBreadcrumb).not.toHaveBeenCalled();

      jest.clearAllMocks();

      // Test with value > 1 (should be treated as 1)
      const overAnalytics = new ThemeAnalytics({
        enabled: true,
        sampleRate: 1.5,
      });
      overAnalytics.recordThemeSwitch('light', 'dark', 1000, 1100);
      expect(Sentry.addBreadcrumb).toHaveBeenCalled();
    });
  });

  describe('主题切换记录', () => {
    it('should record theme switch with correct metrics', () => {
      analytics.recordThemeSwitch('light', 'dark', 1000, 1150, true);

      expect(Sentry.addBreadcrumb).toHaveBeenCalledWith({
        category: 'theme-performance',
        message: 'Theme switched from light to dark',
        level: 'info',
        data: {
          duration: 150,
          supportsViewTransitions: true,
          viewport: { width: 1024, height: 768 },
        },
      });
    });

    it('should update usage statistics', () => {
      analytics.recordThemeSwitch('light', 'dark', 1000, 1100);
      analytics.recordThemeSwitch('dark', 'light', 1200, 1300);

      const stats = analytics.getUsageStatistics();

      expect(stats).toHaveLength(2);

      // Check that both themes are recorded
      const darkStats = stats.find((s) => s.theme === 'dark');
      const lightStats = stats.find((s) => s.theme === 'light');

      expect(darkStats).toBeDefined();
      expect(darkStats!.theme).toBe('dark');
      expect(darkStats!.count).toBe(1);
      expect(darkStats!.sessionDuration).toBe(0); // First occurrence has 0 duration

      expect(lightStats).toBeDefined();
      expect(lightStats!.theme).toBe('light');
      expect(lightStats!.count).toBe(1);
      expect(lightStats!.sessionDuration).toBe(0); // First occurrence has 0 duration

      // lastUsed should be the timestamp when the theme was last used
      expect(typeof darkStats!.lastUsed).toBe('number');
      expect(typeof lightStats!.lastUsed).toBe('number');
    });

    it('should trigger performance issue warning for slow switches', () => {
      analytics.recordThemeSwitch('light', 'dark', 1000, 1250, false); // 250ms > 100ms threshold

      expect(Sentry.captureMessage).toHaveBeenCalledWith(
        'Slow theme switch detected: 250ms',
        'warning',
      );

      expect(Sentry.setContext).toHaveBeenCalledWith(
        'theme-performance-issue',
        {
          duration: 250,
          threshold: 100,
          fromTheme: 'light',
          toTheme: 'dark',
          supportsViewTransitions: false,
          viewport: { width: 1024, height: 768 },
        },
      );
    });

    it('should handle missing window dimensions gracefully', () => {
      Object.defineProperty(window, 'innerWidth', {
        value: undefined,
        writable: true,
      });
      Object.defineProperty(window, 'innerHeight', {
        value: undefined,
        writable: true,
      });

      expect(() => {
        analytics.recordThemeSwitch('light', 'dark', 1000, 1100);
      }).not.toThrow();
    });
  });

  describe('性能统计', () => {
    beforeEach(() => {
      // Add some test data
      analytics.recordThemeSwitch('light', 'dark', 1000, 1050, true); // 50ms
      analytics.recordThemeSwitch('dark', 'system', 1100, 1200, false); // 100ms
      analytics.recordThemeSwitch('system', 'light', 1300, 1450, true); // 150ms (slow)
    });

    it('should calculate performance summary correctly', () => {
      const summary = analytics.getPerformanceSummary();

      expect(summary).toEqual({
        avgSwitchTime: 100, // (50 + 100 + 150) / 3
        maxSwitchTime: 150,
        minSwitchTime: 50,
        totalSwitches: 3,
        slowSwitches: 1, // Only 150ms > 100ms threshold
        viewTransitionsUsage: 2, // 2 out of 3 used View Transitions
      });
    });

    it('should handle empty metrics gracefully', () => {
      const emptyAnalytics = new ThemeAnalytics();
      const summary = emptyAnalytics.getPerformanceSummary();

      expect(summary).toEqual({
        avgSwitchTime: 0,
        maxSwitchTime: 0,
        minSwitchTime: 0,
        totalSwitches: 0,
        slowSwitches: 0,
        viewTransitionsUsage: 0,
      });
    });
  });

  describe('内存管理和数据清理', () => {
    it('should clean up old data beyond 24 hours', () => {
      const oldTimestamp = Date.now() - 25 * 60 * 60 * 1000; // 25 hours ago

      // Mock old timestamp for first record
      jest.spyOn(Date, 'now').mockReturnValueOnce(oldTimestamp);
      analytics.recordThemeSwitch(
        'light',
        'dark',
        oldTimestamp,
        oldTimestamp + 100,
      );

      // Add recent record
      jest.spyOn(Date, 'now').mockReturnValue(Date.now());
      analytics.recordThemeSwitch('dark', 'light', 1000, 1100);

      const summary = analytics.getPerformanceSummary();
      expect(summary.totalSwitches).toBe(1); // Only recent record should remain
    });

    it('should limit data to maximum metrics count', () => {
      // Add more than 1000 records (the limit)
      for (let i = 0; i < 1005; i++) {
        analytics.recordThemeSwitch('light', 'dark', i * 100, i * 100 + 50);
      }

      const summary = analytics.getPerformanceSummary();
      expect(summary.totalSwitches).toBeLessThanOrEqual(1000);
    });

    it('should handle cleanup with empty metrics array', () => {
      // Create analytics with no data
      const emptyAnalytics = new ThemeAnalytics({
        enabled: true,
        sampleRate: 1.0,
      });

      // Trigger cleanup by adding a record
      emptyAnalytics.recordThemeSwitch('light', 'dark', 1000, 1100);

      const summary = emptyAnalytics.getPerformanceSummary();
      expect(summary.totalSwitches).toBe(1);
    });

    it('should handle Date.now() throwing an error during cleanup', () => {
      const originalDateNow = Date.now;
      let callCount = 0;

      // Mock Date.now to throw error on cleanup call
      jest.spyOn(Date, 'now').mockImplementation(() => {
        callCount++;
        if (callCount > 2) {
          // After initial calls for recording
          throw new Error('Date.now failed');
        }
        return originalDateNow();
      });

      // When Date.now throws during execution, the error should propagate
      expect(() => {
        analytics.recordThemeSwitch('light', 'dark', 1000, 1100);
      }).toThrow('Date.now failed');
    });

    it('should handle memory cleanup with corrupted timestamp data', () => {
      // Add a record with valid timestamp
      analytics.recordThemeSwitch('light', 'dark', 1000, 1100);

      // Manually corrupt the metrics array
      const metricsArray = (analytics as any).performanceMetrics;
      metricsArray.push({
        switchDuration: 100,
        fromTheme: 'light',
        toTheme: 'dark',
        timestamp: null, // Invalid timestamp
        userAgent: 'test',
        viewportSize: { width: 1024, height: 768 },
        supportsViewTransitions: false,
      });

      // Should handle corrupted data gracefully
      expect(() => {
        analytics.recordThemeSwitch('dark', 'light', 2000, 2100);
      }).not.toThrow();
    });

    it('should handle extremely large number of metrics', () => {
      // Test with a very large number to ensure memory limits work
      const largeAnalytics = new ThemeAnalytics({
        enabled: true,
        sampleRate: 1.0,
      });

      // Add 2000 records (double the limit)
      for (let i = 0; i < 2000; i++) {
        largeAnalytics.recordThemeSwitch(
          'light',
          'dark',
          i * 100,
          i * 100 + 50,
        );
      }

      const summary = largeAnalytics.getPerformanceSummary();
      expect(summary.totalSwitches).toBeLessThanOrEqual(1000);

      // Verify that the most recent records are kept
      const metrics = (largeAnalytics as any).performanceMetrics;
      expect(metrics.length).toBeLessThanOrEqual(1000);
      if (metrics.length > 0) {
        // Last record should be the most recent
        expect(metrics[metrics.length - 1].switchDuration).toBe(50);
      }
    });

    it('should handle cleanup when filter operation fails', () => {
      // Add enough records to trigger cleanup
      for (let i = 0; i < 1005; i++) {
        analytics.recordThemeSwitch('light', 'dark', i * 100, i * 100 + 50);
      }

      // Mock Array.prototype.filter to throw an error on the next call
      const filterSpy = jest
        .spyOn(Array.prototype, 'filter')
        .mockImplementationOnce(() => {
          throw new Error('Filter operation failed');
        });

      // When filter fails during cleanup, the error should propagate
      expect(() => {
        analytics.recordThemeSwitch('light', 'dark', 200000, 200100);
      }).toThrow('Filter operation failed');

      // Restore original filter
      filterSpy.mockRestore();
    });

    it('should handle slice operation failure during cleanup', () => {
      // Add records to trigger cleanup
      for (let i = 0; i < 1005; i++) {
        analytics.recordThemeSwitch('light', 'dark', i * 100, i * 100 + 50);
      }

      // Mock Array.prototype.slice to throw an error
      const sliceSpy = jest
        .spyOn(Array.prototype, 'slice')
        .mockImplementationOnce(() => {
          throw new Error('Slice operation failed');
        });

      // This test should actually expect the error to be thrown since we're mocking a core operation
      expect(() => {
        analytics.recordThemeSwitch('dark', 'light', 200000, 200100);
      }).toThrow('Slice operation failed');

      // Restore original slice
      sliceSpy.mockRestore();
    });
  });

  describe('用户行为分析', () => {
    it('should analyze switch patterns', () => {
      analytics.recordThemeSwitch('light', 'dark', 1000, 1100);
      analytics.recordThemeSwitch('light', 'dark', 1200, 1300); // Same pattern
      analytics.recordThemeSwitch('dark', 'light', 1400, 1500); // Different pattern

      // Access private property for testing
      const patterns = (analytics as any).switchPatterns;

      expect(patterns).toHaveLength(2);
      expect(
        patterns.find(
          (p: any) => p.sequence[0] === 'light' && p.sequence[1] === 'dark',
        ).frequency,
      ).toBe(2);
    });

    it('should record theme preferences', () => {
      analytics.recordThemePreference('dark');

      expect(Sentry.setUser).toHaveBeenCalledWith({
        themePreference: 'dark',
      });

      expect(Sentry.addBreadcrumb).toHaveBeenCalledWith({
        category: 'theme',
        message: 'Theme preference set to dark',
        level: 'info',
        data: {
          theme: 'dark',
          timestamp: 1000,
        },
      });
    });
  });

  describe('性能报告发送', () => {
    beforeEach(() => {
      analytics.recordThemeSwitch('light', 'dark', 1000, 1100, true);
    });

    it('should send performance report to Sentry', () => {
      analytics.sendPerformanceReport();

      expect(Sentry.addBreadcrumb).toHaveBeenCalledWith(
        expect.objectContaining({
          category: 'theme-analytics',
          message: 'Theme performance report',
          level: 'info',
        }),
      );

      expect(Sentry.setMeasurement).toHaveBeenCalledWith(
        'theme.avg_switch_time',
        100,
      );
      expect(Sentry.setMeasurement).toHaveBeenCalledWith(
        'theme.max_switch_time',
        100,
      );
      expect(Sentry.setMeasurement).toHaveBeenCalledWith(
        'theme.total_switches',
        1,
      );
      expect(Sentry.setMeasurement).toHaveBeenCalledWith(
        'theme.slow_switches_ratio',
        0,
      );
      expect(Sentry.setMeasurement).toHaveBeenCalledWith(
        'theme.view_transitions_usage',
        1,
      );
    });

    it('should handle disabled analytics gracefully', () => {
      const disabledAnalytics = new ThemeAnalytics({ enabled: false });

      expect(() => disabledAnalytics.sendPerformanceReport()).not.toThrow();
    });
  });

  describe('边缘情况处理', () => {
    it('should handle undefined navigator.userAgent', () => {
      Object.defineProperty(navigator, 'userAgent', {
        value: undefined,
        writable: true,
      });

      expect(() => {
        analytics.recordThemeSwitch('light', 'dark', 1000, 1100);
      }).not.toThrow();
    });

    it('should handle missing window object', () => {
      const originalWindow = global.window;
      // @ts-expect-error - Testing edge case
      delete global.window;

      expect(() => {
        analytics.recordThemeSwitch('light', 'dark', 1000, 1100);
      }).not.toThrow();

      // Restore window
      global.window = originalWindow;
    });

    it('should handle zero duration switches', () => {
      analytics.recordThemeSwitch('light', 'dark', 1000, 1000); // 0ms duration

      const summary = analytics.getPerformanceSummary();
      expect(summary.minSwitchTime).toBe(0);
      expect(summary.avgSwitchTime).toBe(0);
    });

    it('should handle negative duration switches', () => {
      analytics.recordThemeSwitch('light', 'dark', 1100, 1000); // Negative duration

      const summary = analytics.getPerformanceSummary();
      expect(summary.minSwitchTime).toBe(-100);
    });
  });
});
